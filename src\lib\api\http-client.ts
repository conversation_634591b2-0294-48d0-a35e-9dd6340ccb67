/**
 * HTTP客户端配置和通用请求函数
 */

import type { RequestConfig, RetryRequestConfig, ApiError, QueryParamsOptions } from './types';
import { tokenManager } from '../token-manager';

// API基础配置
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

/**
 * 构建查询参数字符串
 */
export function buildQueryParams(
  params: Record<string, any>,
  options: QueryParamsOptions = {}
): string {
  const { excludeEmpty = true, excludeNull = true, excludeUndefined = true } = options;
  
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (excludeUndefined && value === undefined) return;
    if (excludeNull && value === null) return;
    if (excludeEmpty && value === '') return;
    
    searchParams.append(key, value.toString());
  });
  
  return searchParams.toString();
}

/**
 * 获取存储的认证token
 * 支持服务端渲染时从 cookies 获取
 */
function getAuthToken(request?: Request): string | null {
  // 服务端渲染时，尝试从 cookies 获取
  if (typeof window === 'undefined') {
    if (request) {
      const cookies = request.headers.get('cookie');
      if (cookies) {
        const match = cookies.match(/auth_token=([^;]+)/);
        return match ? decodeURIComponent(match[1]) : null;
      }
    }
    return null;
  }

  // 客户端从 localStorage 获取
  return localStorage.getItem('auth_token');
}

/**
 * 处理API响应
 */
async function handleResponse<T>(response: Response, retryConfig?: RetryRequestConfig): Promise<T> {
  if (!response.ok) {
    let errorData: any = {};
    try {
      errorData = await response.json();
    } catch {
      // 如果响应不是JSON格式，使用默认错误信息
    }

    // 如果是401错误且有重试配置，尝试刷新token
    if (response.status === 401 && retryConfig) {
      try {
        return await tokenManager.handleUnauthorized(retryConfig);
      } catch (refreshError) {
        // 刷新失败，抛出原始错误
        const error: ApiError = {
          code: response.status,
          message: errorData.message || '认证失败，请重新登录',
          details: errorData
        };
        throw error;
      }
    }

    const error: ApiError = {
      code: response.status,
      message: errorData.message || `HTTP error! status: ${response.status}`,
      details: errorData
    };

    throw error;
  }

  const jsonResponse = await response.json();

  // 返回完整响应，让各个组件自己决定如何处理
  return jsonResponse;
}

/**
 * 通用请求函数（带认证）
 */
export async function request<T>(
  url: string,
  config: RequestConfig & { astroRequest?: Request } = {}
): Promise<T> {
  const { astroRequest, ...restConfig } = config;
  const token = getAuthToken(astroRequest);
  const { params, timeout = 10000, ...requestInit } = restConfig;
  
  // 构建完整URL
  let fullUrl = `${API_BASE_URL}${url}`;
  if (params) {
    const queryString = buildQueryParams(params);
    if (queryString) {
      fullUrl += (url.includes('?') ? '&' : '?') + queryString;
    }
  }
  
  // 设置默认请求头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(requestInit.headers as Record<string, string>),
  };

  // 添加认证头
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(fullUrl, {
      ...requestInit,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // 构建重试请求配置
    const retryConfig = {
      url: fullUrl,
      method: requestInit.method || 'GET',
      headers,
      body: requestInit.body as string,
      signal: controller.signal
    };

    return handleResponse<T>(response, retryConfig);
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timeout');
    }
    
    throw error;
  }
}

/**
 * 不带认证的请求函数（用于登录等公开接口）
 */
export async function requestWithoutAuth<T>(
  url: string,
  config: RequestConfig = {}
): Promise<T> {
  const { params, timeout = 10000, ...requestInit } = config;
  
  // 构建完整URL
  let fullUrl = `${API_BASE_URL}${url}`;
  if (params) {
    const queryString = buildQueryParams(params);
    if (queryString) {
      fullUrl += (url.includes('?') ? '&' : '?') + queryString;
    }
  }
  
  // 设置默认请求头
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...requestInit.headers,
  };
  
  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(fullUrl, {
      ...requestInit,
      headers,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return handleResponse<T>(response);
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timeout');
    }
    
    throw error;
  }
}

/**
 * GET请求的便捷方法
 */
export function get<T>(
  url: string,
  params?: Record<string, any>,
  config?: { astroRequest?: Request }
): Promise<T> {
  return request<T>(url, { method: 'GET', params, ...config });
}

/**
 * POST请求的便捷方法
 */
export function post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return request<T>(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
    ...config,
  });
}

/**
 * PUT请求的便捷方法
 */
export function put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return request<T>(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
    ...config,
  });
}

/**
 * PATCH请求的便捷方法
 */
export function patch<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return request<T>(url, {
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
    ...config,
  });
}

/**
 * DELETE请求的便捷方法
 */
export function del<T>(url: string, config?: RequestConfig): Promise<T> {
  return request<T>(url, { method: 'DELETE', ...config });
}
