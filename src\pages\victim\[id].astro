---
import Layout from '../../layouts/Layout.astro'
import Header from '../../components/vue/Header.vue'
import Footer4Col from '../../components/react/Footer4Col.tsx'
import VictimDetail from '@/components/astro/VictimDetail.astro'
import { victimApi } from '@/lib/api'

// 获取路由参数
const { id } = Astro.params

// 将id转换为数字ID
const victimId = parseInt(id as string)

// 验证ID是否有效
if (isNaN(victimId)) {
  return Astro.redirect('/404')
}

let victimData
let needsAuth = false

try {
  // 从API获取受害者数据，传递 Astro.request 以支持服务端认证
  victimData = await victimApi.getDetail(victimId, Astro.request)
} catch (err: any) {
  console.error('获取受害者数据失败:', err)
  // 检查是否是401错误（需要登录）
  if (err?.code === 401) {
    needsAuth = true
  } else {
    // 其他错误，重定向到404页面
    return Astro.redirect('/404')
  }
}

const title = victimData ? `${victimData.post_title} - 受害者详情 - 威胁情报数据中心` : '受害者详情 - 威胁情报数据中心'
const description = victimData ? `${victimData.post_title} 受到 ${victimData.group_name?.toUpperCase() || '未知组织'} 勒索软件攻击的详细信息，包括攻击时间、公司信息、截图等。` : '受害者详情页面'
---

<Layout title={title} description={description}>
  <Header client:load />
  <main class="pt-20">
    {needsAuth ? (
      <!-- 需要登录的提示页面 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <div class="mb-6">
            <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
              </path>
            </svg>
            <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
            <p class="text-base-content/60 mb-6">此受害者信息需要登录后才能查看</p>
          </div>
          <div class="space-y-3">
            <a href={`/login?redirect=${encodeURIComponent(Astro.url.pathname)}`} class="btn btn-primary btn-block">
              立即登录
            </a>
            <a href="/victims" class="btn btn-ghost btn-block">
              返回受害者列表
            </a>
          </div>
        </div>
      </div>
    ) : victimData ? (
      <VictimDetail victimData={victimData} />
    ) : (
      <!-- 错误状态 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <h2 class="text-2xl font-bold text-base-content mb-2">受害者信息不存在</h2>
          <p class="text-base-content/60 mb-6">抱歉，您访问的受害者信息不存在或已被删除</p>
          <a href="/victims" class="btn btn-primary">返回受害者列表</a>
        </div>
      </div>
    )}
  </main>
  <Footer4Col client:load />
</Layout>
