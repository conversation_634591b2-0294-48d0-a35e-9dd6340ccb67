---
import Layout from '@/layouts/Layout.astro'
import Header from '@/components/vue/Header.vue'
import Footer4Col from '@/components/react/Footer4Col.tsx'
import RansomwareGroupDetail from '@/components/sections/RansomwareGroupDetail.vue'

// 获取动态路由参数
const { slug } = Astro.params

// 验证slug参数
if (!slug) {
  return Astro.redirect('/ransomware')
}

// 生成默认页面标题（Vue组件会动态更新）
const groupName = slug.charAt(0).toUpperCase() + slug.slice(1)
const pageTitle = `${groupName} - 勒索软件组织详情 - 威胁情报数据中心`
---

<Layout title={pageTitle}>
  <Header client:load />
  <main class="min-h-screen bg-base-100 pt-16 lg:pt-20">
    <!-- 让Vue组件处理认证检查和内容渲染 -->
    <RansomwareGroupDetail slug={slug} client:load />
  </main>
  <Footer4Col client:load />
</Layout>