<template>
  <div class="relative">
    <img
      :src="src"
      :alt="alt"
      class="w-full rounded-lg border border-gray-200/20"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    
    <!-- 加载状态 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 flex items-center justify-center bg-base-200/50 rounded-lg"
    >
      <div class="loading loading-spinner loading-md"></div>
    </div>
    
    <!-- 错误状态 -->
    <div 
      v-if="error" 
      class="absolute inset-0 flex items-center justify-center bg-base-200/50 rounded-lg"
    >
      <div class="text-center">
        <div class="text-base-content/60 mb-2">图片加载失败</div>
        <button 
          @click="retryLoad" 
          class="btn btn-sm btn-outline"
        >
          重试
        </button>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="absolute top-4 right-4" v-if="!loading && !error">
      <button
        @click="openImageInNewTab"
        class="btn btn-sm btn-circle bg-black/50 hover:bg-black/70 text-white border-0"
        title="在新标签页中打开"
      >
        <ExternalLink class="h-4 w-4" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ExternalLink } from 'lucide-vue-next'

interface Props {
  src: string
  alt: string
}

const props = defineProps<Props>()

// 响应式状态
const loading = ref(true)
const error = ref(false)

// 处理图片加载成功
const handleImageLoad = () => {
  loading.value = false
  error.value = false
}

// 处理图片加载错误
const handleImageError = () => {
  loading.value = false
  error.value = true
}

// 重试加载
const retryLoad = () => {
  loading.value = true
  error.value = false
  // 通过改变src触发重新加载
  const img = new Image()
  img.onload = handleImageLoad
  img.onerror = handleImageError
  img.src = props.src
}

// 在新标签页中打开图片
const openImageInNewTab = () => {
  window.open(props.src, '_blank')
}
</script>
