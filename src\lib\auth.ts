// 用户认证相关工具函数
import type { User } from '@/types/api'

/**
 * 检查用户是否已认证
 * @returns {boolean} 如果用户已认证返回 true，否则返回 false
 */
export function isAuthenticated(): boolean {
  // 服务端渲染时返回 false
  if (typeof window === 'undefined') return false;
  // 检查本地存储中是否存在认证令牌
  return !!localStorage.getItem('auth_token')
}

/**
 * 获取认证令牌
 * @returns {string | null} 认证令牌或 null
 */
export function getAuthToken(): string | null {
  // 服务端渲染时返回 null
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token')
}

/**
 * 设置认证令牌
 * @param {string} token - 要设置的认证令牌
 */
export function setAuthToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token)

    // 同时设置 cookie 以支持服务端渲染
    const expires = new Date()
    expires.setTime(expires.getTime() + (24 * 60 * 60 * 1000)) // 24小时后过期
    document.cookie = `auth_token=${encodeURIComponent(token)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`
  }
}

/**
 * 移除认证令牌
 */
export function removeAuthToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')

    // 同时清除 cookie
    document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax'
  }
}

/**
 * 获取刷新令牌
 * @returns {string | null} 刷新令牌或 null
 */
export function getRefreshToken(): string | null {
  // 服务端渲染时返回 null
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('refresh_token')
}

/**
 * 设置刷新令牌
 * @param {string} token - 要设置的刷新令牌
 */
export function setRefreshToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('refresh_token', token)
  }
}

/**
 * 移除刷新令牌
 */
export function removeRefreshToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('refresh_token')
  }
}

/**
 * 获取用户信息
 * @returns {User | null} 用户信息对象或 null
 */
export function getUser(): User | null {
  // 服务端渲染时返回 null
  if (typeof window === 'undefined') return null;

  const userStr = localStorage.getItem('user')
  if (!userStr) return null;

  try {
    // 尝试解析存储的用户信息 JSON 字符串
    return JSON.parse(userStr)
  } catch {
    // 解析失败时返回 null
    return null;
  }
}

/**
 * 设置用户信息
 * @param {User} user - 要设置的用户信息对象
 */
export function setUser(user: User): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('user', JSON.stringify(user))
  }
}

/**
 * 移除用户信息
 */
export function removeUser(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user')
  }
}

/**
 * 检查用户是否已登录（保持兼容性）
 * @returns {boolean} 如果用户已登录返回 true，否则返回 false
 */
export function isLoggedIn(): boolean {
  return isAuthenticated()
}

/**
 * 获取当前用户信息（保持兼容性）
 * @returns {User | null} 当前用户信息或 null
 */
export function getCurrentUser(): User | null {
  return getUser()
}

/**
 * 退出登录
 * 清除所有认证相关的本地存储数据并重定向到登录页面
 */
export function logout(): void {
  removeAuthToken()
  removeRefreshToken()
  removeUser()
  localStorage.removeItem('isLoggedIn') // 保持兼容性
  if (typeof window !== 'undefined') {
    window.location.href = '/login'
  }
}

/**
 * 更新用户信息
 * @param {Partial<User>} userData - 要更新的用户数据（部分字段）
 * @returns {Promise<User>} 返回更新后的完整用户信息
 */
export function updateUser(userData: Partial<User>): Promise<User> {
  return new Promise((resolve) => {
    // 模拟异步更新操作
    setTimeout(() => {
      const currentUser = getCurrentUser();
      if (currentUser) {
        // 合并当前用户信息和新的用户数据
        const updatedUser = { ...currentUser, ...userData };
        localStorage.setItem('user', JSON.stringify(updatedUser));
        resolve(updatedUser);
      }
    }, 1000);
  });
}


