# 威胁情报数据中心 - 前端项目

专业的网络安全威胁情报平台前端应用，基于 Astro + Vue.js + FlyonUI 构建。

## 🚀 技术栈

- **框架**: Astro 5.x
- **UI库**: Vue.js 3.x
- **组件库**: FlyonUI
- **样式**: Tailwind CSS 4.x
- **图标**: Lucide Vue Next
- **开发工具**: TypeScript, Vite
- **Mock数据**: vite-plugin-mock + mockjs

## 📁 项目结构

```text
/
├── public/                 # 静态资源
│   └── favicon.svg
├── src/
│   ├── components/         # 组件目录
│   │   ├── auth/           # 认证相关组件
│   │   │   ├── LoginForm.astro     # 登录表单（Astro组件）
│   │   │   ├── RegisterForm.astro  # 注册表单（Astro组件）
│   │   │   └── ProfileCenter.vue   # 用户中心
│   │   ├── sections/       # 页面区块组件
│   │   │   ├── LatestBlogs.astro   # 最新博客区块
│   │   │   ├── LatestThreats.vue   # 最新威胁区块
│   │   │   ├── UniversalSearch.vue # 通用搜索组件
│   │   │   ├── SearchResults.vue   # 搜索结果组件
│   │   │   ├── ToolsList.vue       # 工具列表
│   │   │   └── ...                 # 其他页面区块
│   │   ├── vue/            # Vue组件
│   │   │   └── Header.vue          # 页面头部
│   │   ├── react/          # React组件
│   │   │   ├── SplineHero.tsx      # 3D英雄区块
│   │   │   ├── GlobeCard.tsx       # 地球仪卡片
│   │   │   └── Footer4Col.tsx      # 四列页脚
│   │   └── ui/             # UI组件
│   │       ├── AvatarCropper.vue   # 头像裁剪器
│   │       ├── Btn09.tsx           # 特效按钮
│   │       ├── globe.tsx           # 3D地球组件
│   │       ├── Toast.vue           # 通用Toast提示组件
│   │       ├── ToastContainer.vue  # Toast容器组件
│   │       └── ...                 # 其他UI组件
│   ├── layouts/            # 布局组件
│   │   └── Layout.astro
│   ├── lib/                # 工具库
│   │   ├── api/            # API接口模块
│   │   ├── auth.ts         # 认证工具
│   │   ├── utils.ts        # 工具函数
│   │   └── ...
│   ├── composables/        # Vue组合式函数
│   │   ├── useToast.ts     # Toast提示系统
│   │   └── useAuth.ts      # 认证状态管理
│   ├── pages/              # 页面
│   │   ├── index.astro     # 首页
│   │   ├── login.astro     # 登录页
│   │   ├── register.astro  # 注册页
│   │   └── ...             # 其他页面
│   ├── styles/             # 样式文件
│   │   └── global.css
│   └── types/              # TypeScript类型定义
│       └── api.ts
├── mock/                   # Mock数据
│   ├── dashboard.ts
│   ├── threat-intelligence.ts
│   ├── vulnerability.ts
│   └── security-events.ts
└── package.json
```

## 🧞 开发命令

所有命令都在项目根目录下的终端中运行：

| 命令                      | 说明                                             |
| :------------------------ | :----------------------------------------------- |
| `pnpm install`            | 安装依赖                                         |
| `pnpm dev`                | 启动开发服务器 `localhost:4322`                  |
| `pnpm build`              | 构建生产版本到 `./dist/`                         |
| `pnpm preview`            | 本地预览构建结果                                 |
| `pnpm astro ...`          | 运行 Astro CLI 命令                              |
| `pnpm astro -- --help`    | 获取 Astro CLI 帮助                              |

## ✨ 功能特性

- 🎨 **现代化UI设计**: 基于 FlyonUI 组件库，提供专业的威胁情报界面
- 🌙 **FlyonUI Black主题**: 采用FlyonUI官方Black主题，专业的黑白配色设计
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🔄 **实时数据**: 集成 Mock API，模拟真实的威胁情报数据
- 🎯 **语义化颜色**: 使用FlyonUI语义化颜色系统，确保设计一致性
- 📢 **通用Toast系统**: 统一的消息提示组件，支持多种类型和位置
- ⚡ **高性能**: 基于 Astro 的静态站点生成，加载速度极快

## 🎨 设计系统

### 主题配置
项目使用 **FlyonUI Black主题** 的官方颜色配置，提供专业的黑白配色方案：
- 使用FlyonUI语义化颜色系统
- 自动适配暗色模式
- 保证设计一致性和可维护性

### 颜色使用方式
推荐使用FlyonUI的语义化类名：
```css
.bg-primary        /* 主色背景 */
.text-primary      /* 主色文字 */
.bg-base-100       /* 基础背景色 */
.text-base-content /* 基础内容色 */
.border-primary    /* 主色边框 */
```

### 组件库
项目使用 FlyonUI 组件库，主要通过CSS类使用：
- 直接使用FlyonUI的CSS类（如 `btn`, `card`, `badge` 等）
- Toast（消息提示）- 自定义Vue组件
- AvatarCropper（头像裁剪器）- 自定义Vue组件
- 各种业务组件（威胁情报卡片、勒索组织卡片等）

## 🔧 开发指南

### Toast 消息提示系统

项目集成了通用的Toast消息提示系统，支持多种类型和位置的消息提示。

#### 基本使用
```typescript
import { useToast } from '@/composables/useToast'

const { showSuccess, showError, showWarning, showInfo } = useToast()

// 显示不同类型的提示
showSuccess('操作成功！')
showError('操作失败，请重试')
showWarning('请注意安全风险')
showInfo('这是一条信息提示')
```

#### 高级配置
```typescript
import { useToast } from '@/composables/useToast'

const { showToast } = useToast()

// 自定义位置和持续时间
showToast({
  message: '自定义提示消息',
  type: 'success',
  position: 'top-center',  // top-center | top-right | bottom-center | bottom-right
  duration: 3000           // 显示时长（毫秒）
})
```

#### 在页面中集成
确保在页面中添加 `ToastContainer` 组件：
```astro
---
import ToastContainer from '@/components/ui/ToastContainer.vue'
---

<Layout>
  <!-- 页面内容 -->
  <ToastContainer client:load />
</Layout>
```

### 使用 FlyonUI 组件
参考 [FlyonUI 官方文档](https://flyonui.com/) 了解更多组件使用方法。

### 创建新页面
1. 在 `src/pages/` 目录下创建 `.astro` 文件
2. 使用 Layout 组件包装页面内容
3. 导入需要的 Vue 组件并添加 `client:load` 指令

### API 集成
项目已配置 Mock API，真实环境下可以：
1. 修改 `src/lib/api.ts` 中的 `API_BASE_URL`
2. 更新相应的接口类型定义
3. 处理认证和错误处理逻辑

## 📝 待办事项

- [ ] 集成图表库（如 Chart.js 或 ECharts）
- [ ] 添加数据表格组件
- [ ] 实现用户认证流程
- [ ] 添加更多页面（威胁情报列表、详情页等）
- [ ] 集成真实后端API
- [ ] 添加单元测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
