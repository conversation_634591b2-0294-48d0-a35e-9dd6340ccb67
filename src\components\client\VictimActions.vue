<template>
  <div class="flex items-center gap-2">
    <!-- 复制信息按钮 -->
    <button
      v-if="!actionType || actionType === 'copy'"
      @click="copyVictimInfo"
      class="btn btn-outline btn-sm"
      title="复制信息"
    >
      <Copy class="h-4 w-4" />
      复制
    </button>
    
    <!-- 复制暗网链接按钮 -->
    <button
      v-if="actionType === 'darkweb'"
      @click="copyDarkWebLink"
      class="btn btn-ghost btn-sm"
      title="复制暗网链接"
    >
      <Copy class="h-4 w-4" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { Copy } from 'lucide-vue-next'
import { useToast } from '@/composables/useToast'

interface VictimData {
  post_title: string
  website: string
  group_name?: string
  discovered: string | null
  published: string | null
  country: string
  description: string
  post_url: string
}

interface Props {
  victimData: VictimData
  actionType?: 'copy' | 'darkweb'
}

const props = defineProps<Props>()

// Toast功能
const { showSuccess, showError } = useToast()

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取国家名称
const getCountryName = (code: string) => {
  const countries: Record<string, string> = {
    'CN': '中国',
    'US': '美国',
    'UK': '英国',
    'DE': '德国',
    'FR': '法国',
    'JP': '日本',
    'KR': '韩国',
    'CA': '加拿大',
    'AU': '澳大利亚'
  }
  return countries[code] || code
}

// 复制受害者信息
const copyVictimInfo = async () => {
  const info = `受害者: ${props.victimData.post_title}
网站: ${props.victimData.website}
勒索软件组织: ${props.victimData.group_name?.toUpperCase() || '未知组织'}
发现日期: ${props.victimData.discovered ? formatDate(props.victimData.discovered) : '未知'}
发布日期: ${props.victimData.published ? formatDate(props.victimData.published) : '未知'}
国家: ${getCountryName(props.victimData.country)}
描述: ${props.victimData.description}`

  try {
    await navigator.clipboard.writeText(info)
    showSuccess('受害者信息已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    showError('复制失败，请手动复制')
  }
}

// 复制暗网链接
const copyDarkWebLink = async () => {
  try {
    await navigator.clipboard.writeText(props.victimData.post_url)
    showSuccess('暗网链接已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    showError('复制失败，请手动复制')
  }
}
</script>
