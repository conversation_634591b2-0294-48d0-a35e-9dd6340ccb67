---
import { 
  Calendar, 
  Clock, 
  Globe, 
  Info, 
  Camera, 
  Database, 
  ExternalLink 
} from 'lucide-react'
import VictimActions from '../client/VictimActions.vue'
import ImageViewer from '../client/ImageViewer.vue'
import { parseMarkdown } from '@/lib/markdown'
import type { Victim } from '@/types/api'

interface Props {
  victimData: Victim
}

const { victimData } = Astro.props

// 工具函数
const getInitials = (title: string) => {
  return title.split('.')[0].substring(0, 2).toUpperCase()
}

const getCountryName = (code: string) => {
  const countries: Record<string, string> = {
    'CN': '中国',
    'US': '美国', 
    'UK': '英国',
    'DE': '德国',
    'FR': '法国',
    'JP': '日本',
    'KR': '韩国',
    'CA': '加拿大',
    'AU': '澳大利亚'
  }
  return countries[code] || code
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateChinese = (dateString: string) => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}年${month}月${day}日`
}

// 解析描述内容
const parsedDescription = parseMarkdown(victimData.description ?? '')

// 准备客户端组件需要的数据
const victimInfo = {
  post_title: victimData.post_title,
  website: victimData.website,
  group_name: victimData.group_name,
  discovered: victimData.discovered,
  published: victimData.published,
  country: victimData.country,
  description: victimData.description,
  post_url: victimData.post_url,
  screenshot: victimData.screenshot
}
---

<div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
  <!-- 面包屑导航 -->
  <nav class="mb-6 sm:mb-8">
    <div class="breadcrumbs text-sm">
      <ul>
        <li>
          <a href="/" class="text-primary hover:text-primary/80">首页</a>
        </li>
        <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
        <li>
          <a href="/victims" class="text-primary hover:text-primary/80">受害者</a>
        </li>
        <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
        <li aria-current="page" class="text-base-content/70 truncate max-w-xs">
          {victimData.post_title}
        </li>
      </ul>
    </div>
  </nav>

  <!-- 受害者头部信息 -->
  <div class="card bg-base-100 border border-gray-200/20">
    <div class="card-body p-4 sm:p-6 lg:p-8">
      <div class="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8">
        <!-- 左侧企业logo -->
        <div class="w-full sm:w-full lg:w-80 h-32 sm:h-40 lg:h-48 flex-shrink-0 overflow-hidden rounded-lg bg-base-200/30 flex items-center justify-center">
          <div class="text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
              <span class="text-2xl font-bold text-primary">{getInitials(victimData.post_title)}</span>
            </div>
            <div class="text-sm font-medium text-base-content">{victimData.post_title}</div>
            <div class="text-xs text-base-content/60">{getCountryName(victimData.country)}</div>
          </div>
        </div>

        <!-- 右侧信息 -->
        <div class="flex-1">
          <!-- 标题和标签 -->
          <div class="mb-4 sm:mb-6">
            <div class="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4 flex-wrap">
              <span class="badge badge-error">
                {victimData.group_name?.toUpperCase() || '未知组织'}
              </span>
              <span class="badge badge-warning">
                已确认攻击
              </span>
              <span class="badge badge-outline">
                {getCountryName(victimData.country)}
              </span>
            </div>

            <h1 class="text-2xl sm:text-3xl font-bold text-base-content mb-2">{victimData.post_title}</h1>

            <div class="text-sm text-base-content/70 mb-3 sm:mb-4">
              {victimData.description}
            </div>
          </div>

          <!-- 基本信息网格 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- 发现日期 -->
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div class="text-xs sm:text-sm text-base-content/60">发现日期</div>
                <div class="font-medium text-sm text-base-content">
                  {victimData.discovered ? formatDateChinese(victimData.discovered) : '未知'}
                </div>
              </div>
            </div>

            <!-- 发布日期 -->
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div class="text-xs sm:text-sm text-base-content/60">发布日期</div>
                <div class="font-medium text-sm text-base-content">
                  {victimData.published ? formatDateChinese(victimData.published) : '未知'}
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center gap-2">
              <VictimActions victimData={victimInfo} client:load />
              {victimData.website && (
                <a
                  href={`https://${victimData.website}`}
                  target="_blank"
                  class="btn btn-primary btn-sm"
                >
                  <ExternalLink className="h-4 w-4" />
                  访问
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 详细信息 -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6 sm:mt-8">
    <!-- 左侧主要信息 -->
    <div class="lg:col-span-2 space-y-6">
      <!-- 公司详情 -->
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4 sm:p-6">
          <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
            <Info className="h-5 w-5 text-primary" />
            公司详情
          </h3>
          <div class="prose prose-sm max-w-none text-base-content/80" set:html={parsedDescription}></div>
        </div>
      </div>

      <!-- 攻击截图 -->
      {victimData.screenshot && (
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Camera className="h-5 w-5 text-primary" />
              攻击截图
            </h3>
            <ImageViewer 
              src={victimData.screenshot}
              alt={`${victimData.post_title} 攻击截图`}
              client:load 
            />
          </div>
        </div>
      )}

      <!-- 攻击信息 -->
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4 sm:p-6">
          <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            攻击信息
          </h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div class="p-4 bg-base-200/30 rounded-lg">
              <div class="text-sm text-base-content/60 mb-1">攻击状态</div>
              <div class="font-medium">已确认攻击</div>
            </div>
            <div class="p-4 bg-base-200/30 rounded-lg">
              <div class="text-sm text-base-content/60 mb-1">活动状态</div>
              <div class="font-medium">{victimData.activity || '未知'}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 信息窃取统计 -->
      {victimData.infostealer && (
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Database className="h-5 w-5 text-primary" />
              信息窃取统计
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-warning mb-2">{victimData.infostealer.employees}</div>
                <div class="text-sm text-base-content/60">员工数据</div>
              </div>
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-error mb-2">{victimData.infostealer.users}</div>
                <div class="text-sm text-base-content/60">用户数据</div>
              </div>
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-info mb-2">{victimData.infostealer.total}</div>
                <div class="text-sm text-base-content/60">总计</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>

    <!-- 右侧辅助信息 -->
    <div class="space-y-6">


      <!-- 修改记录 -->
      {victimData.modifications && victimData.modifications.length > 0 && (
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              修改记录
            </h3>
            <div class="space-y-3">
              {victimData.modifications.map((mod) => (
                <div class="flex items-start gap-3 p-3 bg-base-200/30 rounded-lg">
                  <div class="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <div class="font-medium text-sm">{mod.description}</div>
                    <div class="text-xs text-base-content/60">{formatDateChinese(mod.date)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <!-- 相关链接 -->
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4 sm:p-6">
          <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
            <ExternalLink className="h-5 w-5 text-primary" />
            相关链接
          </h3>
          <div class="space-y-3">
            {victimData.website && (
              <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
                <div>
                  <div class="font-medium text-sm">官方网站</div>
                  <div class="text-xs text-base-content/60 truncate">{victimData.website}</div>
                </div>
                <a
                  href={`https://${victimData.website}`}
                  target="_blank"
                  class="btn btn-ghost btn-sm"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
            )}

            {victimData.post_url && (
              <div class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
                <div>
                  <div class="font-medium text-sm">暗网链接</div>
                  <div class="text-xs text-base-content/60">勒索组织发布页面</div>
                </div>
                <VictimActions victimData={victimInfo} actionType="darkweb" client:load />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
